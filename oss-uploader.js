const OSS = require('ali-oss');
const fs = require('fs');
const path = require('path');

/**
 * 阿里云OSS上传类
 */
class OssUploader {
  /**
   * 构造函数
   * @param {Object} config OSS配置
   */
  constructor(config) {
    this.config = config;
    
    // 如果是测试模式，不创建真正的OSS客户端
    if (config.testMode) {
      this.client = null;
      console.log('OSS上传器运行在测试模式');
    } else {
      this.client = new OSS({
        region: config.region || 'oss-cn-hangzhou',
        accessKeyId: config.accessKeyId,
        accessKeySecret: config.accessKeySecret,
        bucket: config.bucket,
        secure: config.secure !== undefined ? config.secure : true
      });
    }
  }
  
  /**
   * 上传文件到OSS
   * @param {string} localPath 本地文件路径
   * @param {string} ossPath OSS上的路径，不传则使用文件名
   * @returns {Object} 上传结果
   */
  async uploadFile(localPath, ossPath = null) {
    try {
      if (!fs.existsSync(localPath)) {
        throw new Error(`文件不存在: ${localPath}`);
      }
      
      // 如果没有指定OSS路径，则使用文件名
      const targetPath = ossPath || path.basename(localPath);
      
      console.log(`开始上传文件 ${localPath} 到 OSS: ${targetPath}`);
      
      // 测试模式下模拟上传
      if (this.config.testMode) {
        console.log(`测试模式: 模拟上传文件 ${localPath} 到 OSS: ${targetPath}`);
        return {
          success: true,
          url: `https://${this.config.bucket}.${this.config.region}.aliyuncs.com/${targetPath}`,
          name: targetPath,
          result: { status: 200 }
        };
      }
      
      // 真实上传
      const result = await this.client.put(targetPath, localPath);
      
      console.log(`文件上传成功: ${result.url}`);
      return {
        success: true,
        url: result.url,
        name: targetPath,
        result
      };
    } catch (error) {
      console.error(`文件上传失败: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * 上传文件到OSS并指定目录
   * @param {string} localPath 本地文件路径
   * @param {string} directory OSS上的目录
   * @returns {Object} 上传结果
   */
  async uploadFileToDirectory(localPath, directory) {
    const fileName = path.basename(localPath);
    const ossPath = directory ? `${directory.replace(/\/+$/, '')}/${fileName}` : fileName;
    return this.uploadFile(localPath, ossPath);
  }
  
  /**
   * 上传文件到OSS并按日期组织目录
   * @param {string} localPath 本地文件路径
   * @returns {Object} 上传结果
   */
  async uploadFileWithDatePath(localPath) {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    
    const directory = `${year}/${month}/${day}`;
    return this.uploadFileToDirectory(localPath, directory);
  }
  
  /**
   * 检查文件是否存在
   * @param {string} ossPath OSS上的路径
   * @returns {boolean} 是否存在
   */
  async isFileExist(ossPath) {
    // 测试模式下总是返回false
    if (this.config.testMode) {
      return false;
    }
    
    try {
      await this.client.head(ossPath);
      return true;
    } catch (error) {
      if (error.code === 'NoSuchKey') {
        return false;
      }
      throw error;
    }
  }
  
  /**
   * 从配置文件创建OSS上传器
   * @param {string} configPath 配置文件路径
   * @returns {OssUploader} OSS上传器实例
   */
  static fromConfigFile(configPath) {
    try {
      const config = require(configPath);
      return new OssUploader(config);
    } catch (error) {
      console.error(`加载OSS配置文件失败: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * 从环境变量创建OSS上传器
   * @returns {OssUploader} OSS上传器实例
   */
  static fromEnv() {
    const config = {
      region: process.env.OSS_REGION || 'oss-cn-hangzhou',
      accessKeyId: process.env.OSS_ACCESS_KEY_ID,
      accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET,
      bucket: process.env.OSS_BUCKET,
      secure: process.env.OSS_SECURE !== 'false'
    };
    
    if (!config.accessKeyId || !config.accessKeySecret || !config.bucket) {
      throw new Error('OSS配置不完整，请检查环境变量');
    }
    
    return new OssUploader(config);
  }
  
  /**
   * 创建测试模式的OSS上传器
   * @returns {OssUploader} OSS上传器实例
   */
  static createTestMode() {
    const config = {
      region: 'oss-cn-hangzhou',
      accessKeyId: 'test_key',
      accessKeySecret: 'test_secret',
      bucket: 'test_bucket',
      testMode: true
    };
    
    return new OssUploader(config);
  }
}

module.exports = OssUploader; 