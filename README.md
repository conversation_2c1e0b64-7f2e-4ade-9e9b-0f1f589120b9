# 美团商家后台数据导出工具

这是一个自动化工具，用于从美团商家后台导出数据，并可选择性地将数据上传到阿里云OSS。

## 功能特点

- 自动登录美团商家后台（需要预先保存登录状态）
- 按日期范围查询数据
- 自动下载Excel数据文件
- 将Excel转换为CSV格式
- 支持上传到阿里云OSS存储
- 支持自定义日期查询
- 支持无头模式运行
- 支持钉钉机器人通知执行结果

## 安装

```bash
# 克隆仓库
git clone https://github.com/yourusername/dianping.pw.git
cd dianping.pw

# 安装依赖
npm install
```

## 使用方法

### 基本用法

```bash
# 使用默认参数（昨天的数据）
node main.js

# 指定日期
node main.js --date 2025-05-20

# 无头模式运行
node main.js --headless true

# 上传到OSS
node main.js --upload true

# 指定最大等待时间（分钟）
node main.js --maxWaitTime 10

# 测试模式（模拟OSS上传）
node main.js --upload true --testMode true
```

### 命令行参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--date` | 指定查询日期，格式为YYYY-MM-DD | 昨天 |
| `--headless` | 是否以无头模式运行 | false |
| `--auth` | 认证文件路径 | auth.json |
| `--upload` | 是否上传到OSS | false |
| `--ossConfig` | OSS配置文件路径 | 无（使用环境变量） |
| `--ossDir` | OSS上传目录 | 无（按日期组织） |
| `--maxWaitTime` | 等待文件生成的最大时间（分钟） | 5 |
| `--testMode` | 是否使用OSS测试模式 | false |
| `--notify` | 是否发送钉钉通知 | true |

## OSS配置

### 使用环境变量

设置以下环境变量：

```bash
export OSS_REGION=oss-cn-hangzhou
export OSS_ACCESS_KEY_ID=your_access_key_id
export OSS_ACCESS_KEY_SECRET=your_access_key_secret
export OSS_BUCKET=your_bucket_name
```

### 使用配置文件

创建一个JSON配置文件（例如`oss-config.json`）：

```json
{
  "region": "oss-cn-hangzhou",
  "accessKeyId": "your_access_key_id",
  "accessKeySecret": "your_access_key_secret",
  "bucket": "your_bucket_name"
}
```

然后使用以下命令：

```bash
node main.js --upload true --ossConfig ./oss-config.json
```

## 认证

本工具需要预先保存美团商家后台的登录状态。请确保`auth.json`文件存在并包含有效的登录信息。

## 钉钉通知

本工具支持通过钉钉机器人发送执行结果通知。

### 配置钉钉机器人

1. 在钉钉群中添加自定义机器人
2. 获取机器人的Webhook地址
3. 在`main.js`中设置`DINGTALK_WEBHOOK`常量为你的Webhook地址

### 通知类型

- 执行成功通知：当数据成功导出并上传时
- 执行失败通知：当没有找到数据或发生错误时

### 禁用通知

如果不需要发送通知，可以使用以下命令：

```bash
node main.js --notify false
```

## 文件结构

- `main.js` - 主程序入口
- `browser.js` - 浏览器操作相关功能
- `utils.js` - 工具函数
- `oss-uploader.js` - 阿里云OSS上传功能
- `dingtalk-notifier.js` - 钉钉通知功能

## 注意事项

- 请确保网络连接稳定
- 首次运行时需要手动登录并保存状态
- OSS上传需要有效的阿里云账号和权限
- 文件下载可能需要等待一段时间，系统默认最多等待5分钟 