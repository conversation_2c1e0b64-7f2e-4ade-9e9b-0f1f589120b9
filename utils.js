const fs = require('fs');
const path = require('path');
const xlsx = require('xlsx');

/**
 * 延时函数
 * @param {number} ms 延时毫秒数
 * @returns {Promise} 延时Promise
 */
const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

/**
 * 创建截图目录
 * @param {string} dateStr 可选的日期字符串，用于创建子目录
 * @returns {string} 截图目录路径
 */
const createScreenshotDir = (dateStr) => {
  // 基础截图目录
  const baseScreenshotDir = path.join(process.cwd(), 'screenshots');
  if (!fs.existsSync(baseScreenshotDir)) {
    fs.mkdirSync(baseScreenshotDir);
  }
  
  // 如果提供了日期，创建日期子目录
  if (dateStr) {
    const dateScreenshotDir = path.join(baseScreenshotDir, dateStr);
    if (!fs.existsSync(dateScreenshotDir)) {
      fs.mkdirSync(dateScreenshotDir);
    }
    return dateScreenshotDir;
  }
  
  // 如果没有提供日期，使用当前日期创建子目录
  const today = new Date();
  const todayStr = formatDate(today).replace(/-/g, '');
  const todayScreenshotDir = path.join(baseScreenshotDir, todayStr);
  if (!fs.existsSync(todayScreenshotDir)) {
    fs.mkdirSync(todayScreenshotDir);
  }
  
  return todayScreenshotDir;
};

/**
 * 截图函数
 * @param {Page} page Playwright页面对象
 * @param {string} name 截图名称
 * @param {string} dateStr 可选的日期字符串，用于创建子目录
 */
const takeScreenshot = async (page, name, dateStr) => {
  // 如果提供了日期字符串，使用它；否则使用当前日期
  const effectiveDateStr = dateStr || formatDate(new Date()).replace(/-/g, '');
  const screenshotDir = createScreenshotDir(effectiveDateStr);
  const screenshotPath = path.join(screenshotDir, `${name}_${new Date().toISOString().replace(/:/g, '-')}.png`);
  await page.screenshot({ path: screenshotPath, fullPage: true });
  console.log(`截图已保存: ${screenshotPath}`);
};

/**
 * 根据执行结果处理截图目录
 * @param {boolean} success 执行是否成功
 * @param {string} dateStr 日期字符串，用于定位截图目录
 */
const handleScreenshotDir = (success, dateStr) => {
  // 确定截图目录路径
  const baseScreenshotDir = path.join(process.cwd(), 'screenshots');
  const dateScreenshotDir = path.join(baseScreenshotDir, dateStr);
  
  if (success) {
    // 如果执行成功，删除截图目录
    if (fs.existsSync(dateScreenshotDir)) {
      try {
        // 删除目录中的所有文件
        const files = fs.readdirSync(dateScreenshotDir);
        for (const file of files) {
          fs.unlinkSync(path.join(dateScreenshotDir, file));
        }
        
        // 删除目录
        fs.rmdirSync(dateScreenshotDir);
        console.log(`执行成功，已删除截图目录: ${dateScreenshotDir}`);
      } catch (error) {
        console.error(`删除截图目录失败: ${error.message}`);
      }
    } else {
      console.log(`截图目录不存在，无需删除: ${dateScreenshotDir}`);
    }
  } else {
    // 如果执行失败，保留截图目录
    console.log(`执行失败，保留截图目录以供检查: ${dateScreenshotDir}`);
  }
};

/**
 * 将xlsx转换为csv的函数
 * @param {string} xlsxPath xlsx文件路径
 * @returns {string} 生成的CSV文件路径
 */
const convertXlsxToCsv = async (xlsxPath) => {
  try {
    const workbook = xlsx.readFile(xlsxPath);
    const sheetName = workbook.SheetNames[0];
    const csvPath = xlsxPath.replace('.xlsx', '.csv');
    
    xlsx.writeFile(workbook, csvPath, { bookType: 'csv' });
    console.log(`已将 ${xlsxPath} 转换为 ${csvPath}`);
    return csvPath;
  } catch (error) {
    console.error(`转换文件失败: ${error.message}`);
    throw error;
  }
};

/**
 * 解析命令行参数
 * @returns {Object} 解析后的参数对象
 */
const parseArgs = () => {
  const args = process.argv.slice(2);
  const params = {};
  
  for (let i = 0; i < args.length; i++) {
    if (args[i].startsWith('--')) {
      const key = args[i].slice(2);
      const value = args[i + 1] && !args[i + 1].startsWith('--') ? args[i + 1] : true;
      params[key] = value;
      if (value !== true) i++;
    }
  }
  
  return params;
};

/**
 * 获取日期范围
 * @param {string} dateStr 可选的日期字符串 (YYYY-MM-DD)
 * @returns {Object} 包含开始和结束日期的对象
 */
const getDateRange = (dateStr) => {
  let startDate, endDate;
  
  if (dateStr) {
    // 如果提供了日期，使用该日期作为开始日期，次日作为结束日期
    startDate = new Date(dateStr);
    endDate = new Date(dateStr);
    endDate.setDate(endDate.getDate() + 1);
    
    console.log(`使用指定的日期: ${dateStr}，解析为 ${startDate.toISOString().split('T')[0]}`);
  } else {
    // 默认使用昨天到今天的日期范围
    endDate = new Date();
    startDate = new Date();
    startDate.setDate(startDate.getDate() - 1);
    
    console.log(`使用默认的日期范围: 昨天到今天`);
  }
  
  // 确保日期对象是有效的
  if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
    throw new Error(`无效的日期格式: ${dateStr}`);
  }
  
  return {
    startDate,
    endDate,
    startDay: startDate.getDate(), // 日期的天数
    endDay: endDate.getDate(),     // 日期的天数
    startMonth: startDate.getMonth() + 1, // 月份，JavaScript月份从0开始
    endMonth: endDate.getMonth() + 1,     // 月份
    formattedStartDate: formatDate(startDate),
    formattedEndDate: formatDate(endDate)
  };
};

/**
 * 格式化日期为YYYY-MM-DD格式
 * @param {Date} date 日期对象
 * @returns {string} 格式化后的日期字符串
 */
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

module.exports = {
  delay,
  takeScreenshot,
  convertXlsxToCsv,
  parseArgs,
  getDateRange,
  formatDate,
  handleScreenshotDir
}; 