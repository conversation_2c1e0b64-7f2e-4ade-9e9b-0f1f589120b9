const axios = require('axios');

/**
 * 钉钉通知类
 */
class DingTalkNotifier {
  /**
   * 构造函数
   * @param {string} webhook 钉钉机器人webhook地址
   * @param {string} keyword 关键词
   */
  constructor(webhook, keyword = '美团') {
    this.webhook = webhook;
    this.keyword = keyword;
  }

  /**
   * 发送文本消息
   * @param {string} content 消息内容
   * @param {boolean} isAtAll 是否@所有人
   * @returns {Promise} 发送结果
   */
  async sendText(content, isAtAll = false) {
    try {
      // 确保消息中包含关键词
      const messageContent = content.includes(this.keyword) ? content : `${this.keyword}：${content}`;
      
      const response = await axios.post(this.webhook, {
        msgtype: 'text',
        text: {
          content: messageContent
        },
        at: {
          isAtAll
        }
      });

      return {
        success: response.data.errcode === 0,
        data: response.data
      };
    } catch (error) {
      console.error('发送钉钉通知失败:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 发送执行失败通知
   * @param {string} taskName 任务名称
   * @param {string} error 错误信息
   * @returns {Promise} 发送结果
   */
  async sendFailureNotification(taskName, error) {
    const content = `❌ ${taskName}执行失败\n错误信息: ${error}\n时间: ${new Date().toLocaleString()}`;
    return this.sendText(content);
  }

  /**
   * 发送执行成功通知
   * @param {string} taskName 任务名称
   * @returns {Promise} 发送结果
   */
  async sendSuccessNotification(taskName) {
    const content = `✅ ${taskName}执行成功\n时间: ${new Date().toLocaleString()}`;
    return this.sendText(content);
  }
}

module.exports = DingTalkNotifier; 