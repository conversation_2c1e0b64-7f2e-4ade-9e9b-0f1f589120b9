const { chromium } = require('playwright');
const { delay, takeScreenshot } = require('./utils');

/**
 * 初始化浏览器
 * @param {Object} options 浏览器配置选项
 * @returns {Object} 包含browser和page的对象
 */
const initBrowser = async (options = {}) => {
  const browser = await chromium.launch({
    headless: options.headless !== undefined ? options.headless : false
  });
  
  const context = await browser.newContext({
    storageState: options.authFile || 'auth.json',
    downloadTimeout: options.downloadTimeout || 120000
  });
  
  const page = await context.newPage();
  
  return { browser, context, page };
};

/**
 * 导航到美团商家后台
 * @param {Page} page Playwright页面对象
 * @param {string} dateStr 日期字符串
 */
const navigateToMeituanBackend = async (page, dateStr) => {
  await page.goto('https://ecom.meituan.com/meishi/?cate=6102#https://ecom.meituan.com/bizdeal/coupon-order-manage/write-off-details');
  await delay(10000);
  
  console.log("点击订单管理");
  // await page.getByRole('menuitem', { name: ' 订单管理' }).locator('div').nth(1).click();
  // await delay(2000);
  
  await takeScreenshot(page, '01_订单管理页面', dateStr);
};

/**
 * 选择核销状态
 * @param {Page} page Playwright页面对象
 * @param {string} status 核销状态，可选值：'已核销'、'已撤销'
 * @param {string} dateStr 日期字符串，用于截图目录
 */
const selectVerificationStatus = async (page, status, dateStr) => {
  console.log(`选择核销状态: ${status}`);
  
  try {
    // 获取iframe内容
    const frameHandle = await page.locator('#iframeContainer').contentFrame();
    
    // 尝试获取页面内容以便调试
    console.log("尝试获取页面内容...");
    try {
      const pageContent = await page.evaluate(() => document.body.innerHTML);
      console.log(`页面DOM长度: ${pageContent.length}`);
    } catch (e) {
      console.log("获取页面内容失败:", e.message);
    }
    
    // 尝试点击选项卡
    console.log(`尝试点击${status}选项卡...`);
    
    // 方法1：通过选项卡文本定位
    try {
      // 查找包含状态文本的选项卡
      const tabSelector = `.mtd-tabs-nav-item:has-text("${status}")`;
      await frameHandle.locator(tabSelector).click({ timeout: 5000 });
      console.log(`通过选项卡文本选择了状态: ${status}`);
      await delay(2000);
      await takeScreenshot(page, `选择${status}状态后`, dateStr);
      return;
    } catch (err) {
      console.log(`通过选项卡文本选择状态失败: ${err.message}`);
    }
    
    // 方法2：通过索引定位选项卡
    try {
      const tabItems = await frameHandle.locator('.mtd-tabs-nav-item').all();
      console.log(`找到 ${tabItems.length} 个选项卡`);
      
      if (tabItems.length >= 2) {
        // 已核销通常是第一个选项卡，已撤销是第二个
        const tabIndex = status === '已核销' ? 0 : 1;
        await tabItems[tabIndex].click();
        console.log(`通过索引(${tabIndex})选择了状态: ${status}`);
        await delay(2000);
        await takeScreenshot(page, `选择${status}状态后`, dateStr);
        return;
      }
    } catch (err) {
      console.log(`通过索引选择状态失败: ${err.message}`);
    }
    
    // 方法3：通过JavaScript直接点击
    try {
      await frameHandle.evaluate((targetStatus) => {
        const tabs = document.querySelectorAll('.mtd-tabs-nav-item');
        for (const tab of tabs) {
          if (tab.textContent.includes(targetStatus)) {
            tab.click();
            return true;
          }
        }
        return false;
      }, status);
      console.log(`通过JavaScript选择了状态: ${status}`);
      await delay(2000);
      await takeScreenshot(page, `选择${status}状态后_JS`, dateStr);
    } catch (err) {
      console.log(`通过JavaScript选择状态失败: ${err.message}`);
    }
    
    // 无论是否成功选择状态，都等待一段时间让页面稳定
    await delay(3000);
  } catch (error) {
    console.error(`选择核销状态失败:`, error);
    await takeScreenshot(page, `选择核销状态失败`, dateStr);
  }
};

/**
 * 选择日期范围
 * @param {Page} page Playwright页面对象
 * @param {number} startDay 开始日期
 * @param {number} endDay 结束日期
 * @param {number} startMonth 开始月份
 * @param {number} endMonth 结束月份
 * @param {string} dateStr 日期字符串，用于截图目录
 */
const selectDateRange = async (page, startDay, endDay, startMonth, endMonth, dateStr) => {
  console.log("点击日期选择器");
  await page.locator('#iframeContainer').contentFrame().getByRole('textbox', { name: '开始年月日 - 截止年月日' }).click();
  await delay(2000);
  
  await takeScreenshot(page, '02_日期选择器', dateStr);
  
  // 直接选择日期，不需要先点击此刻按钮
  const frameHandle = await page.locator('#iframeContainer').contentFrame();
  
  // 使用传入的月份参数作为预期月份
  const expectedStartMonth = startMonth;
  const expectedEndMonth = endMonth;
  
  console.log(`预期开始日期: ${startMonth}-${startDay}`);
  console.log(`预期结束日期: ${endMonth}-${endDay}`);
  
  // 查找日期面板中的日期元素
  console.log(`尝试选择起始日期: ${startDay}`);
  
  // 选择开始日期
  await selectDateByValue(frameHandle, startDay, expectedStartMonth);
  await takeScreenshot(page, '04_选择第一个日期后', dateStr);
  
  // 验证开始日期选择是否正确
  const startDateSelected = await verifyDateSelection(frameHandle, startDay, expectedStartMonth);
  if (!startDateSelected) {
    console.error(`开始日期选择验证失败，预期日期: ${startDay}日，预期月份: ${expectedStartMonth}月`);
    await takeScreenshot(page, '开始日期选择验证失败', dateStr);
    throw new Error('日期选择验证失败，程序终止');
  }
  
  // 选择结束日期
  console.log(`尝试选择结束日期: ${endDay}`);
  await selectDateByValue(frameHandle, endDay, expectedEndMonth);
  await takeScreenshot(page, '05_选择第二个日期后', dateStr);
  
  // 验证结束日期选择是否正确
  const endDateSelected = await verifyDateSelection(frameHandle, endDay, expectedEndMonth);
  if (!endDateSelected) {
    console.error(`结束日期选择验证失败，预期日期: ${endDay}日，预期月份: ${expectedEndMonth}月`);
    await takeScreenshot(page, '结束日期选择验证失败', dateStr);
    throw new Error('日期选择验证失败，程序终止');
  }
  
  // 点击选择时间按钮
  console.log("点击选择时间按钮");
  await frameHandle.getByRole('button', { name: '选择时间' }).click();
  await delay(2000);
  
  // 选择时间为00:00
  await selectTime(page);
  
  console.log("点击确认按钮");
  await frameHandle.getByRole('button', { name: '确认' }).click();
  await delay(2000);
};

/**
 * 通过日期值选择日期
 * @param {Frame} frameHandle iframe的Frame对象
 * @param {number} day 要选择的日期
 * @param {number} expectedMonth 预期选择的月份
 */
const selectDateByValue = async (frameHandle, day, expectedMonth) => {
  try {
    // 使用传入的预期月份作为目标月份
    const targetMonth = expectedMonth;
    
    console.log(`目标日期: ${day}日, 目标月份: ${targetMonth}月`);
    
    // 首先检查左右两个日期面板，看是否已经有我们需要的月份
    const datePanels = await frameHandle.locator('.mtd-date-calendar-content.active').all();
    console.log(`找到 ${datePanels.length} 个活动的日期面板`);
    
    // 标记是否找到了目标月份的面板
    let foundTargetMonthPanel = false;
    let targetPanel = null;
    
    // 检查每个面板的月份
    for (let i = 0; i < datePanels.length; i++) {
      try {
        const monthBtn = await datePanels[i].locator('.mtd-date-calendar-month-btn').first();
        const monthText = await monthBtn.textContent();
        console.log(`面板 ${i+1} 的月份: ${monthText}`);
        
        // 从月份文本中提取月份数字
        const monthMatch = monthText.match(/(\d+)月/);
        if (monthMatch) {
          const panelMonth = parseInt(monthMatch[1], 10);
          if (panelMonth === targetMonth) {
            foundTargetMonthPanel = true;
            targetPanel = datePanels[i];
            console.log(`在面板 ${i+1} 中找到目标月份 ${targetMonth}月`);
            break;
          }
        }
      } catch (e) {
        console.log(`读取面板 ${i+1} 的月份时出错: ${e.message}`);
      }
    }
    
    if (foundTargetMonthPanel && targetPanel) {
      // 如果找到了目标月份的面板，直接在该面板中选择日期
      console.log(`直接在找到的目标月份面板中选择日期 ${day}`);
      
      try {
        // 在目标面板中查找匹配的日期元素
        // 首先尝试使用更精确的选择器
        await targetPanel.locator(`.mtd-date-panel-data-wrapper:not(.not-current-month) .mtd-date-panel-data:text("${day}")`).first().click({ timeout: 3000 });
        console.log(`使用精确选择器在目标面板中选择了日期 ${day}`);
        await delay(2000);
        return true;
      } catch (error) {
        console.log(`无法使用精确选择器在目标面板中找到日期 ${day}，尝试使用JavaScript: ${error.message}`);
        
        // 尝试使用JavaScript直接查找并点击
        try {
          const clicked = await frameHandle.evaluate((targetDay, targetMonthVal) => {
            // 首先找到显示目标月份的面板
            const panels = document.querySelectorAll('.mtd-date-calendar-content.active');
            for (const panel of panels) {
              const monthBtn = panel.querySelector('.mtd-date-calendar-month-btn');
              if (monthBtn && monthBtn.textContent.includes(`${targetMonthVal}月`)) {
                // 在该面板中查找日期元素
                const dateElements = panel.querySelectorAll('.mtd-date-panel-data');
                for (const element of dateElements) {
                  if (element.textContent.trim() === targetDay.toString()) {
                    // 确认该元素在当前月份
                    const parent = element.parentElement;
                    if (!parent.className.includes('not-current-month')) {
                      element.click();
                      return true;
                    }
                  }
                }
              }
            }
            return false;
          }, day, targetMonth);
          
          if (clicked) {
            console.log(`使用JavaScript在目标面板中成功选择了日期 ${day}`);
            await delay(2000);
            return true;
          } else {
            console.log(`使用JavaScript在目标面板中未能找到日期 ${day}，将尝试常规方法`);
          }
        } catch (jsError) {
          console.log(`使用JavaScript选择日期时出错: ${jsError.message}`);
        }
      }
    }
    
    // 如果没有找到目标月份的面板，或者在目标面板中未找到日期，使用常规方法
    console.log(`使用常规方法选择日期 ${day}`);
    
    // 首先检查当前显示的月份
    const currentMonthText = await frameHandle.locator('.mtd-date-calendar-month-btn').first().textContent();
    console.log(`当前显示的月份: ${currentMonthText}`);
    
    // 从月份文本中提取月份数字
    const monthMatch = currentMonthText.match(/(\d+)月/);
    let displayedMonth = 0;
    if (monthMatch) {
      displayedMonth = parseInt(monthMatch[1], 10);
      console.log(`解析出的当前显示月份: ${displayedMonth}月`);
    }
    
    // 如果当前显示的月份不是目标月份，需要切换
    if (displayedMonth !== targetMonth) {
      console.log(`需要从 ${displayedMonth}月 切换到 ${targetMonth}月`);
      
      // 点击月份按钮显示月份选择面板
      await frameHandle.locator('.mtd-date-calendar-month-btn').first().click();
      await delay(1000);
      
      // 选择目标月份 - 使用更精确的选择器并添加first()方法
      const targetMonthText = `${targetMonth}月`;
      
      try {
        // 尝试使用更精确的选择器
        await frameHandle.locator(`.mtd-month-panel-list.selected-month .mtd-month-panel-list-data:text("${targetMonthText}")`).click({ timeout: 2000 });
      } catch (e) {
        console.log(`无法使用精确选择器选择月份，尝试使用first()方法: ${e.message}`);
        // 如果精确选择器失败，使用first()方法
        await frameHandle.locator(`.mtd-month-panel-list-data:text("${targetMonthText}")`).first().click({ timeout: 2000 });
      }
      
      await delay(2000);
      console.log(`已切换到目标月份: ${targetMonth}月`);
      
      // 验证月份是否切换成功
      const updatedMonthText = await frameHandle.locator('.mtd-date-calendar-month-btn').first().textContent();
      console.log(`切换后的月份显示: ${updatedMonthText}`);
      
      // 再次检查月份是否正确
      const updatedMonthMatch = updatedMonthText.match(/(\d+)月/);
      if (updatedMonthMatch) {
        const updatedMonth = parseInt(updatedMonthMatch[1], 10);
        if (updatedMonth !== targetMonth) {
          console.log(`月份切换失败，尝试使用JavaScript直接选择`);
          
          // 使用JavaScript直接选择月份
          await frameHandle.evaluate((targetMonthVal) => {
            const monthElements = document.querySelectorAll('.mtd-month-panel-list-data');
            for (const element of monthElements) {
              if (element.textContent.trim() === `${targetMonthVal}月`) {
                element.click();
                return true;
              }
            }
            return false;
          }, targetMonth);
          
          await delay(2000);
        }
      }
    } else {
      console.log(`当前已经是目标月份: ${targetMonth}月，无需切换`);
    }
    
    // 现在在正确的月份中查找目标日期
    const dateElements = await frameHandle.locator('.mtd-date-panel-data').all();
    console.log(`找到 ${dateElements.length} 个日期元素`);
    
    // 在当前月份中查找匹配的日期元素
    for (const element of dateElements) {
      const text = await element.textContent();
      if (text.trim() === day.toString()) {
        // 确认该元素在当前月份（不包含not-current-month类）
        const parentClass = await element.evaluate(el => el.parentElement.className);
        if (!parentClass.includes('not-current-month')) {
          console.log(`找到当前月份的日期元素 ${day}，尝试点击`);
          await element.click();
          await delay(2000);
          return true;
        }
      }
    }
    
    // 如果使用常规方法未找到，尝试使用更精确的选择器
    console.log(`尝试使用选择器直接查找当前月份的日期 ${day}`);
    try {
      // 使用更精确的选择器，排除not-current-month类的元素
      await frameHandle.locator(`.mtd-date-panel-data-wrapper:not(.not-current-month) .mtd-date-panel-data:text("${day}")`).first().click();
      console.log(`使用精确选择器成功选择了日期 ${day}`);
      await delay(2000);
      return true;
    } catch (error) {
      console.log(`无法使用精确选择器找到日期 ${day}，尝试最后的方法`);
      
      // 最后的尝试：使用JavaScript直接查找并点击
      const clicked = await frameHandle.evaluate((targetDay) => {
        const dateElements = document.querySelectorAll('.mtd-date-panel-data');
        for (const element of dateElements) {
          if (element.textContent.trim() === targetDay.toString()) {
            const parent = element.parentElement;
            if (!parent.className.includes('not-current-month')) {
              element.click();
              return true;
            }
          }
        }
        return false;
      }, day);
      
      if (clicked) {
        console.log(`使用JavaScript成功选择了日期 ${day}`);
        await delay(2000);
        return true;
      }
      
      console.error(`无法在当前月份找到日期 ${day}`);
      throw new Error(`无法在当前月份找到日期 ${day}`);
    }
  } catch (error) {
    console.error(`选择日期 ${day} 失败:`, error);
    throw error;
  }
};

/**
 * 选择时间为00:00
 * @param {Page} page Playwright页面对象
 */
const selectTime = async (page) => {
  console.log("选择时间 00:00:00:00");
  await page.locator('#iframeContainer').contentFrame().getByRole('menuitem', { name: '00' }).first().click();
  await delay(1000);
  await page.locator('#iframeContainer').contentFrame().getByRole('menuitem', { name: '00' }).nth(1).click();
  await delay(1000);
  await page.locator('#iframeContainer').contentFrame().getByRole('menuitem', { name: '00' }).nth(2).click();
  await delay(1000);
  await page.locator('#iframeContainer').contentFrame().getByRole('menuitem', { name: '00' }).nth(3).click();
  await delay(1000);
};

/**
 * 验证日期选择是否正确
 * @param {Frame} frameHandle iframe的Frame对象
 * @param {number} day 预期选择的日期
 * @param {number} expectedMonth 预期选择的月份
 * @returns {boolean} 验证是否通过
 */
const verifyDateSelection = async (frameHandle, day, expectedMonth) => {
  try {
    // 获取所有活动的日期面板
    const datePanels = await frameHandle.locator('.mtd-date-calendar-content.active').all();
    console.log(`验证: 找到 ${datePanels.length} 个活动的日期面板`);
    
    // 遍历每个面板，查找包含选中日期的面板
    for (let i = 0; i < datePanels.length; i++) {
      try {
        // 检查面板的月份
        const monthBtn = await datePanels[i].locator('.mtd-date-calendar-month-btn').first();
        const monthText = await monthBtn.textContent();
        console.log(`验证: 面板 ${i+1} 的月份: ${monthText}`);
        
        // 从月份文本中提取月份数字
        const monthMatch = monthText.match(/(\d+)月/);
        if (!monthMatch) continue;
        
        const panelMonth = parseInt(monthMatch[1], 10);
        
        // 在该面板中查找选中的日期元素
        const selectedElements = await datePanels[i].locator('.selected-date .mtd-date-panel-data').all();
        
        for (const element of selectedElements) {
          const text = await element.textContent();
          if (text.trim() === day.toString()) {
            // 找到了选中的日期，检查它是否在当前月份
            const parentClass = await element.evaluate(el => el.parentElement.className);
            if (!parentClass.includes('not-current-month')) {
              console.log(`验证: 在面板 ${i+1} 中找到选中的日期 ${day}，面板月份: ${panelMonth}月，预期月份: ${expectedMonth}月`);
              
              // 检查面板的月份是否符合预期
              return panelMonth === expectedMonth;
            }
          }
        }
      } catch (e) {
        console.log(`验证: 检查面板 ${i+1} 时出错: ${e.message}`);
      }
    }
    
    // 如果在面板中没有找到选中的日期，尝试使用常规方法
    console.log(`验证: 在面板中未找到选中的日期 ${day}，尝试使用常规方法`);
    
    // 获取当前选中的日期元素
    const selectedDateElements = await frameHandle.locator('.selected-date .mtd-date-panel-data').all();
    
    if (selectedDateElements.length === 0) {
      console.error('验证: 未找到选中的日期元素');
      return false;
    }
    
    // 检查选中的日期是否符合预期
    for (const element of selectedDateElements) {
      const text = await element.textContent();
      if (text.trim() === day.toString()) {
        // 检查该元素是否在当前月份
        const parentClass = await element.evaluate(el => el.parentElement.className);
        if (!parentClass.includes('not-current-month')) {
          // 检查当前显示的月份
          const currentMonthText = await frameHandle.locator('.mtd-date-calendar-month-btn').first().textContent();
          const monthMatch = currentMonthText.match(/(\d+)月/);
          
          if (monthMatch) {
            const selectedMonth = parseInt(monthMatch[1], 10);
            console.log(`验证: 选中的日期: ${day}日，当前显示月份: ${selectedMonth}月，预期月份: ${expectedMonth}月`);
            
            return selectedMonth === expectedMonth;
          }
        }
      }
    }
    
    return false;
  } catch (error) {
    console.error(`验证日期选择失败:`, error);
    return false;
  }
};

/**
 * 查询数据
 * @param {Page} page Playwright页面对象
 * @param {string} dateStr 日期字符串，用于截图目录
 * @returns {boolean} 是否有数据
 */
const queryData = async (page, dateStr) => {
  console.log("点击查询按钮");
  
  try {
    // 获取iframe内容
    const frameHandle = await page.locator('#iframeContainer').contentFrame();
    
    // 尝试多种方式找到查询按钮并点击
    try {
      await frameHandle.getByRole('button', { name: '查询' }).click({ timeout: 5000 });
      console.log("通过角色找到并点击了查询按钮");
    } catch (e) {
      console.log("通过角色查找查询按钮失败，尝试其他方法");
      
      try {
        // 尝试通过文本内容查找
        await frameHandle.locator('button:has-text("查询")').first().click({ timeout: 5000 });
        console.log("通过文本内容找到并点击了查询按钮");
      } catch (e2) {
        console.log("通过文本内容查找查询按钮失败，尝试其他方法");
        
        // 尝试通过类选择器查找
        const buttons = await frameHandle.locator('button.mtd-btn').all();
        console.log(`找到 ${buttons.length} 个按钮元素`);
        
        let clicked = false;
        for (const button of buttons) {
          const text = await button.textContent();
          console.log(`按钮文本: ${text}`);
          if (text.includes('查询')) {
            await button.click();
            clicked = true;
            console.log("通过遍历按钮找到并点击了查询按钮");
            break;
          }
        }
        
        if (!clicked) {
          console.log("未能找到查询按钮");
          throw new Error("未能找到查询按钮");
        }
      }
    }
  } catch (error) {
    console.error("点击查询按钮失败:", error);
    await takeScreenshot(page, "查询按钮点击失败", dateStr);
    return false;
  }
  
  await delay(2000);
  await takeScreenshot(page, '06_数据查询结果', dateStr);
  
  // 检查是否有数据
  console.log("检查是否有数据");
  try {
    const rowCount = await page.locator('#iframeContainer').contentFrame().locator('table tbody tr').count();
    const hasData = rowCount > 0;
    console.log(`是否找到数据: ${hasData} (行数: ${rowCount})`);
    return hasData;
  } catch (error) {
    console.error("检查数据行数失败:", error);
    // 尝试通过检查表格内容判断是否有数据
    try {
      const tableContent = await page.locator('#iframeContainer').contentFrame().locator('table').evaluate(table => table.textContent);
      const hasData = tableContent && tableContent.length > 0 && !tableContent.includes('暂无数据');
      console.log(`通过表格内容判断是否有数据: ${hasData}`);
      return hasData;
    } catch (e) {
      console.error("检查表格内容失败:", e);
      return false;
    }
  }
};

/**
 * 检查文件是否生成完成
 * @param {Page} page Playwright页面对象
 * @param {number} maxWaitTimeMinutes 最大等待时间（分钟）
 * @param {string} dateStr 日期字符串，用于截图目录
 * @returns {boolean} 是否找到可下载的文件
 */
const waitForFileReady = async (page, maxWaitTimeMinutes = 5, dateStr) => {
  console.log(`等待文件生成完成，最多等待 ${maxWaitTimeMinutes} 分钟...`);
  
  const maxAttempts = maxWaitTimeMinutes * 12; // 每5秒检查一次
  let attempts = 0;
  
  while (attempts < maxAttempts) {
    attempts++;
    
    // 刷新状态
    await page.locator('#iframeContainer').contentFrame().getByRole('button', { name: '刷新状态' }).click();
    await delay(5000);
    
    // 截图记录当前状态
    if (attempts % 12 === 0) { // 每分钟截图一次
      await takeScreenshot(page, `等待文件生成_${attempts / 12}分钟`, dateStr);
    }
    
    // 检查是否有文件可下载
    const tableContent = await page.locator('#iframeContainer').contentFrame().locator('table').evaluate(table => table.textContent);
    console.log(`第 ${attempts} 次检查，表格内容: ${tableContent.substring(0, 100)}...`);
    
    // 检查是否包含"生成中"文本
    const isGenerating = tableContent.includes('生成中');
    
    // 检查是否有下载链接
    const hasDownloadLink = await page.evaluate(() => {
      const iframe = document.querySelector('#iframeContainer');
      if (!iframe || !iframe.contentDocument) return false;
      
      const links = iframe.contentDocument.querySelectorAll('table a');
      return links.length > 0;
    });
    
    if (!isGenerating && hasDownloadLink) {
      console.log(`文件已生成完成，可以下载（尝试次数: ${attempts}）`);
      return true;
    }
    
    console.log(`文件${isGenerating ? '正在生成中' : '状态未知'}，继续等待...`);
  }
  
  console.log(`已达到最大等待时间 ${maxWaitTimeMinutes} 分钟，将尝试下载`);
  return false;
};

/**
 * 下载数据
 * @param {Page} page Playwright页面对象
 * @param {Object} options 下载选项
 * @param {string} dateStr 日期字符串，用于截图目录
 * @returns {string} 下载的文件路径
 */
const downloadData = async (page, options = {}, dateStr) => {
  const maxWaitTime = options.maxWaitTime || 5; // 默认最多等待5分钟
  
  console.log("找到数据，准备下载...");
  await page.locator('#iframeContainer').contentFrame().getByRole('button', { name: ' 下载数据' }).click();
  await delay(2000);
  
  await page.locator('#iframeContainer').contentFrame().getByRole('button', { name: '去查看' }).click();
  await delay(2000);
  
  await takeScreenshot(page, '08_下载状态页面', dateStr);
  
  // 等待文件生成完成
  await waitForFileReady(page, maxWaitTime, dateStr);
  
  await takeScreenshot(page, '09_文件生成完成', dateStr);
  
  // 使用页面级别的evaluate来获取下载链接
  const downloadPromise = page.waitForEvent('download', { timeout: 120000 });
  
  const clickResult = await page.evaluate(() => {
    const iframe = document.querySelector('#iframeContainer');
    if (!iframe || !iframe.contentDocument) return { success: false, error: 'No iframe found' };
    
    const links = iframe.contentDocument.querySelectorAll('table a');
    if (links.length > 0) {
      try {
        links[0].click();
        return { success: true, count: links.length };
      } catch (e) {
        return { success: false, error: e.toString(), count: links.length };
      }
    }
    return { success: false, count: 0 };
  });
  console.log("JavaScript点击结果:", clickResult);
  
  try {
    console.log("等待下载开始...");
    const download = await downloadPromise;
    const fileName = download.suggestedFilename();
    console.log(`下载的文件名: ${fileName}`);
    
    // 保存下载的文件
    const path = require('path');
    const downloadPath = path.join(process.cwd(), fileName);
    await download.saveAs(downloadPath);
    console.log(`文件已保存到: ${downloadPath}`);
    
    return downloadPath;
  } catch (error) {
    console.error("下载文件失败:", error);
    throw error;
  }
};

module.exports = {
  initBrowser,
  navigateToMeituanBackend,
  selectDateRange,
  queryData,
  downloadData,
  selectVerificationStatus,
  verifyDateSelection
}; 