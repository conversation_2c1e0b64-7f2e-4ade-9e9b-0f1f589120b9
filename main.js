const path = require('path');
const { parseArgs, getDateRange, convertXlsxToCsv, takeScreenshot, formatDate, handleScreenshotDir } = require('./utils');
const { initBrowser, navigateToMeituanBackend, selectDateRange, queryData, downloadData, selectVerificationStatus } = require('./browser');
const OssUploader = require('./oss-uploader');
const DingTalkNotifier = require('./dingtalk-notifier');

// 钉钉机器人webhook地址
const DINGTALK_WEBHOOK = 'https://oapi.dingtalk.com/robot/send?access_token=963f223abac1d17b6bbd6c3073bfef8247b1a3d1fbab7f4595f799fb5db5b131';

/**
 * 检查 verified CSV 是否已存在，存在则直接退出
 */
function checkCsvExistAndExit(dateStr, args, notifier) {
  const verifiedCsvTargetPath = path.join(
    process.cwd(),
    `${dateStr}_verified.csv`
  );
  if (require('fs').existsSync(verifiedCsvTargetPath)) {
    console.log(`文件已存在: ${verifiedCsvTargetPath}，任务已成功完成！`);
    handleScreenshotDir(true, dateStr);
    process.exit(0);
  }
}

/**
 * 主程序入口
 */
async function main() {
  const notifier = new DingTalkNotifier(DINGTALK_WEBHOOK);
  const args = parseArgs(); // 将args提前到try块外，确保在catch块中可用
  
  try {
    // 解析命令行参数
    console.log('命令行参数:', args);
    
    // 获取日期范围
    const dateRange = getDateRange(args.date);
    console.log(`将处理日期范围: ${dateRange.formattedStartDate} 到 ${dateRange.formattedEndDate}`);
    
    // 生成日期字符串，用于截图目录和文件名
    const dateStr = dateRange.startDate.getFullYear() +
      String(dateRange.startDate.getMonth() + 1).padStart(2, '0') +
      String(dateRange.startDate.getDate()).padStart(2, '0');
    
    // 进入主流程前，先检测 verified.csv 是否已存在
    checkCsvExistAndExit(dateStr, args, notifier);
    
    // 初始化浏览器
    const { browser, page } = await initBrowser({
      headless: args.headless === 'true',
      authFile: args.auth || 'auth.json'
    });
    
    try {
      // 导航到美团商家后台
      await navigateToMeituanBackend(page, dateStr);
      
      // 选择日期范围
      await selectDateRange(page, dateRange.startDay, dateRange.endDay, dateRange.startMonth, dateRange.endMonth, dateStr);
      
      // 下载已核销和已撤销的数据
      const results = {
        verified: { success: false, path: null },
        cancelled: { success: false, path: null }
      };
      
      // 先执行一次查询，获取页面DOM
      console.log('执行初始查询，获取页面DOM...');
      await queryData(page, dateStr);
      await takeScreenshot(page, '初始查询结果', dateStr);
      
      // 先处理已核销数据
      try {
        console.log('开始处理已核销数据...');
        await selectVerificationStatus(page, '已核销', dateStr);
        const verifiedHasData = await queryData(page, dateStr);
        
        if (verifiedHasData) {
          try {
            // 下载已核销数据
            const verifiedXlsxPath = await downloadData(page, {
              maxWaitTime: parseInt(args.maxWaitTime || '5')
            }, dateStr);
            
            // 转换为CSV并重命名
            const verifiedCsvPath = await convertXlsxToCsv(verifiedXlsxPath);
            const verifiedNewPath = path.join(
              path.dirname(verifiedCsvPath),
              `${dateStr}_verified.csv`
            );
            
            // 重命名文件
            require('fs').renameSync(verifiedCsvPath, verifiedNewPath);
            console.log(`已核销数据文件已重命名为: ${verifiedNewPath}`);
            
            results.verified = { success: true, path: verifiedNewPath };
          } catch (downloadError) {
            console.error('下载或处理已核销数据失败:', downloadError);
            await takeScreenshot(page, '已核销数据处理失败', dateStr);
          }
        } else {
          console.log('没有找到已核销数据');
        }
      } catch (verifiedError) {
        console.error('处理已核销数据时出错:', verifiedError);
        await takeScreenshot(page, '已核销数据处理错误', dateStr);
      }
      
      // 处理已撤销数据
      try {
        console.log('开始处理已撤销数据...');
        // 重新导航到订单管理页面，以确保状态重置
        await navigateToMeituanBackend(page, dateStr);
        await selectDateRange(page, dateRange.startDay, dateRange.endDay, dateRange.startMonth, dateRange.endMonth, dateStr);
        
        // 先执行一次查询
        await queryData(page, dateStr);
        
        await selectVerificationStatus(page, '已撤销', dateStr);
        const cancelledHasData = await queryData(page, dateStr);
        
        if (cancelledHasData) {
          try {
            // 下载已撤销数据
            const cancelledXlsxPath = await downloadData(page, {
              maxWaitTime: parseInt(args.maxWaitTime || '5')
            }, dateStr);
            
            // 转换为CSV并重命名
            const cancelledCsvPath = await convertXlsxToCsv(cancelledXlsxPath);
            const cancelledNewPath = path.join(
              path.dirname(cancelledCsvPath),
              `${dateStr}_canceled.csv`
            );
            
            // 重命名文件
            require('fs').renameSync(cancelledCsvPath, cancelledNewPath);
            console.log(`已撤销数据文件已重命名为: ${cancelledNewPath}`);
            
            results.cancelled = { success: true, path: cancelledNewPath };
          } catch (downloadError) {
            console.error('下载或处理已撤销数据失败:', downloadError);
            await takeScreenshot(page, '已撤销数据处理失败', dateStr);
          }
        } else {
          console.log('没有找到已撤销数据');
        }
      } catch (cancelledError) {
        console.error('处理已撤销数据时出错:', cancelledError);
        await takeScreenshot(page, '已撤销数据处理错误', dateStr);
      }
      
      // 上传到OSS
      if (args.upload === 'true') {
        if (results.verified.success) {
          try {
            await uploadToOss(results.verified.path, args, dateRange, 'verified');
            console.log('已核销数据上传成功');
          } catch (uploadError) {
            console.error('上传已核销数据到OSS失败:', uploadError);
          }
        }
        
        if (results.cancelled.success) {
          try {
            await uploadToOss(results.cancelled.path, args, dateRange, 'cancelled');
            console.log('已撤销数据上传成功');
          } catch (uploadError) {
            console.error('上传已撤销数据到OSS失败:', uploadError);
          }
        }
      }
      
      // 检查是否有任何数据被处理
      const anyDataProcessed = results.verified.success || results.cancelled.success;
      
      if (anyDataProcessed) {
        console.log('任务完成!');
        
        // 发送成功通知
        if (args.notify !== 'false') {
          await notifier.sendSuccessNotification('美团数据导出');
        }
        
        // 处理截图目录 - 成功时删除
        handleScreenshotDir(true, dateStr);
        
        return { success: true, results };
      } else {
        console.log('没有找到任何数据，任务结束');
        
        // 发送失败通知
        if (args.notify !== 'false') {
          await notifier.sendFailureNotification('美团数据导出', '没有找到任何数据');
        }
        
        // 处理截图目录 - 失败时保留
        handleScreenshotDir(false, dateStr);
        
        return { success: false, reason: 'no_data' };
      }
    } finally {
      // 关闭浏览器
      await browser.close();
    }
  } catch (error) {
    console.error('程序执行出错:', error);
    
    // 发送错误通知
    if (args && args.notify !== 'false') {
      await notifier.sendFailureNotification('美团数据导出', error.message);
    }
    
    // 处理截图目录 - 失败时保留
    const dateStr = args.date ? 
      new Date(args.date).getFullYear() + 
      String(new Date(args.date).getMonth() + 1).padStart(2, '0') + 
      String(new Date(args.date).getDate()).padStart(2, '0') : 
      formatDate(new Date()).replace(/-/g, '');
    handleScreenshotDir(false, dateStr);
    
    return { success: false, error: error.message };
  }
}

/**
 * 上传文件到OSS
 * @param {string} filePath 文件路径
 * @param {Object} args 命令行参数
 * @param {Object} dateRange 日期范围对象
 * @param {string} fileType 文件类型，'verified'表示已核销，'cancelled'表示已撤销
 */
async function uploadToOss(filePath, args, dateRange, fileType = 'verified') {
  try {
    let ossUploader;
    
    // 根据参数选择OSS配置方式
    if (args.ossConfig) {
      console.log(`使用配置文件: ${args.ossConfig}`);
      ossUploader = OssUploader.fromConfigFile(path.resolve(args.ossConfig));
    } else if (args.testMode === 'true') {
      console.log('使用测试模式配置OSS');
      ossUploader = OssUploader.createTestMode();
    } else {
      console.log('使用环境变量配置OSS');
      ossUploader = OssUploader.fromEnv();
    }
    
    // 上传文件到指定前缀
    const ossPrefix = 'third_coupon/dianping/';
    const fileName = path.basename(filePath);
    const ossPath = `${ossPrefix}${fileName}`;
    
    console.log(`上传${fileType === 'verified' ? '已核销' : '已撤销'}文件到OSS: ${ossPath}`);
    const result = await ossUploader.uploadFile(filePath, ossPath);
    
    console.log('OSS上传结果:', result.success ? '成功' : '失败', result.url || result.error);
    return result;
  } catch (error) {
    console.error('OSS上传出错:', error);
    throw error;
  }
}

// 如果直接运行此文件，则执行主函数
if (require.main === module) {
  main().then(result => {
    if (result.success) {
      console.log('程序执行成功!');
      process.exit(0);
    } else {
      console.error('程序执行失败:', result.reason || result.error);
      process.exit(1);
    }
  }).catch(error => {
    console.error('未捕获的错误:', error);
    process.exit(1);
  });
}

module.exports = { main }; 