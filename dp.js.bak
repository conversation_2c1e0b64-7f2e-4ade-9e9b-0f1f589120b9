const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');
const xlsx = require('xlsx');
const util = require('util');
const exec = util.promisify(require('child_process').exec);

// 添加延时函数
const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

// 创建截图目录
const screenshotDir = path.join(process.cwd(), 'screenshots');
if (!fs.existsSync(screenshotDir)) {
  fs.mkdirSync(screenshotDir);
}

// 截图函数
async function takeScreenshot(page, name) {
  const screenshotPath = path.join(screenshotDir, `${name}_${new Date().toISOString().replace(/:/g, '-')}.png`);
  await page.screenshot({ path: screenshotPath, fullPage: true });
  console.log(`截图已保存: ${screenshotPath}`);
}

(async () => {
  let browser, context, page;
  
  try {
    // 获取前一天的日期
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    // 格式化日期为数字
    const yesterdayDate = yesterday.getDate();
    const todayDate = today.getDate();
    
    console.log(`将查询日期: ${yesterdayDate}日到${todayDate}日的数据`);

    browser = await chromium.launch({
      headless: false
    });
    await delay(1000); // 延时1秒
    
    context = await browser.newContext({
      storageState: 'auth.json',
      // 增加下载超时时间
      downloadTimeout: 120000
    });
    await delay(1000); // 延时1秒
    
    page = await context.newPage();
    await delay(1000); // 延时1秒
    
    await page.goto('https://ecom.meituan.com/meishi');
    await delay(2000); // 延长等待时间
    
    console.log("点击订单管理");
    await page.getByRole('menuitem', { name: ' 订单管理' }).locator('div').nth(1).click();
    await delay(2000); // 延长等待时间
    
    // 截图
    await takeScreenshot(page, '01_订单管理页面');
    
    console.log("点击日期选择器");
    await page.locator('#iframeContainer').contentFrame().getByRole('textbox', { name: '开始年月日 - 截止年月日' }).click();
    await delay(2000); // 延长等待时间
    
    // 截图
    await takeScreenshot(page, '02_日期选择器');
    
    console.log("点击此刻按钮");
    await page.locator('#iframeContainer').contentFrame().getByRole('button', { name: '此刻' }).click();
    await delay(2000); // 延长等待时间
    
    console.log("再次点击日期选择器");
    await page.locator('#iframeContainer').contentFrame().getByRole('textbox', { name: '开始年月日 - 截止年月日' }).click();
    await delay(2000); // 延长等待时间
    
    // 截图
    await takeScreenshot(page, '03_日期选择界面');
    
    // 选择前一天的日期 - 使用更精确的选择器
    console.log(`尝试点击日期: ${yesterdayDate}`);
    try {
      // 获取可见的日期选择器元素
      const dateElements = await page.locator('#iframeContainer').contentFrame().locator('.mtd-date-panel-data-wrapper .mtd-date-panel-data').all();
      console.log(`找到 ${dateElements.length} 个日期元素`);
      
      // 查找并点击包含目标日期的元素
      let foundYesterday = false;
      for (const element of dateElements) {
        const text = await element.textContent();
        if (text.trim() === yesterdayDate.toString()) {
          console.log(`找到日期元素 ${yesterdayDate}，尝试点击`);
          await element.click();
          foundYesterday = true;
          break;
        }
      }
      
      if (!foundYesterday) {
        console.log(`未找到日期元素 ${yesterdayDate}，尝试使用其他方法`);
        await page.locator('#iframeContainer').contentFrame().locator(`.mtd-date-panel-data-wrapper .mtd-date-panel-data:text("${yesterdayDate}")`).first().click();
      }
    } catch (error) {
      console.error(`点击日期 ${yesterdayDate} 失败:`, error);
      // 截图记录错误
      await takeScreenshot(page, `error_点击日期${yesterdayDate}`);
      throw error;
    }
    await delay(2000); // 延长等待时间
    
    // 截图
    await takeScreenshot(page, '04_选择第一个日期后');
    
    console.log(`尝试点击日期: ${todayDate}`);
    try {
      // 获取可见的日期选择器元素
      const dateElements = await page.locator('#iframeContainer').contentFrame().locator('.mtd-date-panel-data-wrapper .mtd-date-panel-data').all();
      
      // 查找并点击包含目标日期的元素
      let foundToday = false;
      for (const element of dateElements) {
        const text = await element.textContent();
        if (text.trim() === todayDate.toString()) {
          console.log(`找到日期元素 ${todayDate}，尝试点击`);
          await element.click();
          foundToday = true;
          break;
        }
      }
      
      if (!foundToday) {
        console.log(`未找到日期元素 ${todayDate}，尝试使用其他方法`);
        await page.locator('#iframeContainer').contentFrame().locator(`.mtd-date-panel-data-wrapper .mtd-date-panel-data:text("${todayDate}")`).first().click();
      }
    } catch (error) {
      console.error(`点击日期 ${todayDate} 失败:`, error);
      // 截图记录错误
      await takeScreenshot(page, `error_点击日期${todayDate}`);
      throw error;
    }
    await delay(2000); // 延长等待时间
    
    // 截图
    await takeScreenshot(page, '05_选择第二个日期后');

    await page.locator('#iframeContainer').contentFrame().getByRole('menuitem', { name: '00' }).first().click();
    await delay(1000);
    await page.locator('#iframeContainer').contentFrame().getByRole('menuitem', { name: '00' }).nth(1).click();
    await delay(1000);
    await page.locator('#iframeContainer').contentFrame().getByRole('menuitem', { name: '00' }).nth(2).click();
    await delay(1000);
    await page.locator('#iframeContainer').contentFrame().getByRole('menuitem', { name: '00' }).nth(3).click();
    await delay(1000);

    console.log("点击确认按钮");
    await page.locator('#iframeContainer').contentFrame().getByRole('button', { name: '确认' }).click();
    await delay(2000); // 延长等待时间
    
    console.log("点击查询按钮");
    await page.locator('#iframeContainer').contentFrame().getByRole('button', { name: '查询' }).click();
    await delay(2000); // 延长等待时间
    
    // 截图
    await takeScreenshot(page, '06_数据查询结果');
    
    // 检查是否有数据
    console.log("检查是否有数据");
    const hasData = await page.locator('#iframeContainer').contentFrame().locator('table tbody tr').count() > 0;
    console.log(`是否找到数据: ${hasData}`);
    
    if (hasData) {
      console.log("找到数据，准备下载...");
      await page.locator('#iframeContainer').contentFrame().getByRole('button', { name: ' 下载数据' }).click();
      await delay(2000); // 延长等待时间
      
      await page.locator('#iframeContainer').contentFrame().getByRole('button', { name: '去查看' }).click();
      await delay(2000); // 延长等待时间
      
      // 截图
      await takeScreenshot(page, '08_下载状态页面');
      
      await page.locator('#iframeContainer').contentFrame().getByRole('button', { name: '刷新状态' }).click();
      await delay(5000); // 延长等待时间
      
      // 截图
      await takeScreenshot(page, '09_刷新状态后');
      
      // 检查下载链接是否存在
      const frame = await page.locator('#iframeContainer').contentFrame();
      
      // 打印表格内容以便调试
      const tableHTML = await frame.locator('table').evaluate(element => element.outerHTML);
      console.log("表格HTML结构:", tableHTML.substring(0, 500) + "...");
      
      const tableRows = await frame.locator('table tbody tr').all();
      console.log(`找到 ${tableRows.length} 行数据`);
      
      if (tableRows.length > 0) {
        // 获取第一行的HTML内容
        const firstRowHTML = await tableRows[0].evaluate(element => element.outerHTML);
        console.log("第一行HTML:", firstRowHTML);
        
        // 尝试多种选择器定位链接
        const links = await tableRows[0].locator('a').all();
        console.log(`第一行找到 ${links.length} 个链接`);
        
        // 尝试其他选择器
        const allLinks = await frame.locator('table a').all();
        console.log(`表格中总共找到 ${allLinks.length} 个链接`);
        
        // 尝试通过文本内容找链接
        const downloadLinks = await frame.locator('a:has-text("下载")').all();
        console.log(`找到 ${downloadLinks.length} 个包含"下载"文本的链接`);
        
        // 尝试通过页面执行JavaScript获取链接信息
        const allATags = await page.evaluate(() => {
          const iframe = document.querySelector('#iframeContainer');
          if (!iframe || !iframe.contentDocument) return [];
          
          const links = iframe.contentDocument.querySelectorAll('table a');
          return Array.from(links).map(a => ({
            href: a.getAttribute('href'),
            text: a.textContent,
            className: a.className
          }));
        });
        console.log("所有链接元素:", JSON.stringify(allATags, null, 2));
        
        // 设置下载事件监听
        const downloadPromise = page.waitForEvent('download', { timeout: 120000 });
        
        // 尝试直接通过JavaScript点击第一个下载链接
        const clickResult = await page.evaluate(() => {
          const iframe = document.querySelector('#iframeContainer');
          if (!iframe || !iframe.contentDocument) return { success: false, error: 'No iframe found' };
          
          const links = iframe.contentDocument.querySelectorAll('table a');
          if (links.length > 0) {
            try {
              links[0].click();
              return { success: true, count: links.length };
            } catch (e) {
              return { success: false, error: e.toString(), count: links.length };
            }
          }
          return { success: false, count: 0 };
        });
        console.log("JavaScript点击结果:", clickResult);
        
        // 如果JavaScript点击不成功，尝试其他方法
        if (!clickResult.success && allLinks.length > 0) {
          console.log("尝试点击第一个链接");
          await allLinks[0].click();
        } else if (!clickResult.success && downloadLinks.length > 0) {
          console.log("尝试点击包含'下载'文本的链接");
          await downloadLinks[0].click();
        }
        
        await delay(5000); // 给下载足够的时间启动
        
        try {
          console.log("等待下载开始...");
          const download = await downloadPromise;
          const fileName = download.suggestedFilename();
          console.log(`下载的文件名: ${fileName}`);
          
          // 保存下载的文件
          const downloadPath = path.join(process.cwd(), fileName);
          await download.saveAs(downloadPath);
          console.log(`文件已保存到: ${downloadPath}`);
          
          // 转换xlsx为csv
          await convertXlsxToCsv(downloadPath);
        } catch (downloadError) {
          console.error("下载文件失败:", downloadError);
          await takeScreenshot(page, 'error_download_failed');
        }
      } else {
        console.log("没有找到数据行");
        await takeScreenshot(page, 'error_no_data_rows');
      }
    } else {
      console.log("没有找到可下载的数据");
    }
    
    // 关闭浏览器
    if (browser) {
      await browser.close();
    }
    console.log("脚本执行完成");
    
  } catch (error) {
    console.error("脚本执行出错:", error);
    
    // 出错时也截图
    if (page) {
      await takeScreenshot(page, 'error_final');
    }
    
    // 确保浏览器关闭
    if (browser) {
      await browser.close();
    }
  }
})();

// 将xlsx转换为csv的函数
async function convertXlsxToCsv(xlsxPath) {
  try {
    const workbook = xlsx.readFile(xlsxPath);
    const sheetName = workbook.SheetNames[0];
    const csvPath = xlsxPath.replace('.xlsx', '.csv');
    
    xlsx.writeFile(workbook, csvPath, { bookType: 'csv' });
    console.log(`已将 ${xlsxPath} 转换为 ${csvPath}`);
  } catch (error) {
    console.error(`转换文件失败: ${error.message}`);
  }
}
